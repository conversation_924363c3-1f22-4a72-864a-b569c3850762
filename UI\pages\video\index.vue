<template>
  <view class="container">
    <!-- 直接使用 uni-app 原生 video 组件 -->
    <video id="myVideo" ref="videoPlayer" :src="currentVideo.url" :poster="currentVideo.cover" class="video-player"
      :controls="true" :autoplay="true" :show-progress="true" :show-fullscreen-btn="true" :show-play-btn="true"
      :show-center-play-btn="true" :enable-progress-gesture="false" :page-gesture="false" :direction="0"
      :show-mute-btn="false" :enable-play-gesture="false" @loadedmetadata="onLoadedMetadata" @timeupdate="onTimeUpdate"
      @ended="onVideoEnded" @play="onPlay" @pause="onPause" @fullscreenchange="onFullscreenChange" @waiting="onWaiting"
      @canplay="onCanPlay" @seeking="onSeeking" @seeked="onSeeked"></video>

    <!-- 内容区域 - 仅在非全屏时显示 -->
    <view v-if="!isFullscreen" class="video-content">
      <!-- 视频信息 -->
      <view class="video-info">
        <text class="video-title">{{ currentVideo.title }}</text>
        <view class="video-meta">
          <text class="views">{{ currentVideo.views }}次观看</text>
        </view>
        <text class="description">{{ currentVideo.description }}</text>
      </view>

      <!-- 问答区域 -->
      <VideoQuiz :questions="quizData.questions" :rewardAmount="currentVideo.rewardAmount || 0"
        :videoCompleted="videoCompleted" @submit="onQuizSubmit" @complete="onQuizComplete" />
    </view>
  </view>
</template>

<script>

import VideoQuiz from "../../components/VideoQuiz.vue";
// import { getVideoDetail, getUserVideoProgress } from "@/api/video.js"; // 已改为无认证版本
// import { getBatchDetail } from "@/api/batch.js"; // 已改为无认证版本
// 新的统一API - 已改为无认证版本
// import { createOrGetRecord, updateWatchProgress, startWatching, submitAnswer, grantReward } from "@/api/user-batch-record.js";
// import { getOrCreateIPUser, getCurrentIPUserId } from "@/utils/ip-user.js"; // 暂时不需要
import { wechatLogin as wechatLoginApi } from "@/api/video-user.js";
// 保留旧API作为备用
import { createViewRecord, updateViewProgress } from "@/api/view-record.js";
// 导入媒体文件处理mixin
import mediaCommonMixin from "@/mixins/media-common.js";
// 导入配置工具
import { getApiBaseURL } from "@/utils/config.js";
// 导入微信用户认证服务
import wechatUserService from "@/utils/wechatUserService.js";

export default {
  mixins: [mediaCommonMixin],
  components: {
    VideoQuiz,
  },
  data () {
    return {
      // 视频数据
      videoId: 2, // 默认视频ID
      batchId: null, // 批次ID
      sharerId: null, // 分享人ID
      currentVideo: {}, // 当前视频信息
      quizData: {}, // 问题数据
      duration: 0, // 视频总时长

      // 播放状态
      isFullscreen: false,
      videoCompleted: false,
      maxWatchTime: 0, // 已观看的最大时间点
      currentPlayTime: 0, // 当前播放时间

      // 观看进度监控
      progressTimer: null, // 进度监控定时器
      watchStartTime: null, // 开始观看时间
      totalWatchTime: 0, // 总观看时长
      viewRecordId: null, // 观看记录ID

      // 批次信息
      batchInfo: null, // 批次详情信息
    };
  },

  async onLoad (options) {
    console.log('Video page loaded with options:', options);
    console.log('Current videoId:', this.videoId, 'batchId:', this.batchId, 'sharerId:', this.sharerId);

    // 如果options为空，尝试从URL中解析参数
    if (!options || Object.keys(options).length === 0) {
      console.log('Options为空，尝试从URL解析参数');
      const url = window.location.href;
      const hash = url.split('#')[1];
      if (hash && hash.includes('?')) {
        const queryString = hash.split('?')[1];
        const params = new URLSearchParams(queryString);
        options = {};
        for (let [key, value] of params) {
          options[key] = value;
        }
        console.log('从URL解析的参数:', options);
      }
    }

    // 初始化模拟微信用户（临时方案）
    try {
      console.log('初始化模拟微信用户');
      this.initMockWechatUser();
    } catch (error) {
      console.error('模拟用户初始化失败:', error);
      // 继续执行，不阻断视频播放
    }

    // 页面加载时禁用滚动
    uni.setPageMeta({
      pageStyle: {
        overflow: "hidden",
      },
    });

    // 获取URL参数
    if (options && (options.id || options.videoId)) {
      this.videoId = parseInt(options.id || options.videoId);
      console.log('设置videoId:', this.videoId);
    }
    if (options && options.batchId) {
      this.batchId = parseInt(options.batchId);
      console.log('设置batchId:', this.batchId);
    }
    if (options && options.sharerId) {
      this.sharerId = options.sharerId; // sharerId可能是字符串，不需要parseInt
      console.log('检测到分享人ID:', this.sharerId);
    }

    console.log('参数解析完成 - videoId:', this.videoId, 'batchId:', this.batchId, 'sharerId:', this.sharerId);

    // 加载视频数据
    this.loadVideoData();

    // 开始进度监控
    this.startProgressMonitoring();
  },

  onShow () {
    console.log('onShow called - Current params:', {
      videoId: this.videoId,
      batchId: this.batchId,
      sharerId: this.sharerId
    });

    // 如果参数为空，尝试重新解析
    if (!this.batchId) {
      this.parseUrlParams();
    }

    // 页面显示时恢复播放
    const videoContext = uni.createVideoContext('myVideo', this)
    if (videoContext) {
      videoContext.play()
    }
    // 恢复进度监控
    this.startProgressMonitoring();
  },

  onHide () {
    // 页面隐藏时暂停播放
    const videoContext = uni.createVideoContext('myVideo', this)
    if (videoContext) {
      videoContext.pause()
    }
    // 停止进度监控
    this.stopProgressMonitoring();
  },

  onUnload () {
    // 页面卸载时清理定时器
    this.stopProgressMonitoring();
  },

  methods: {
    // ===== 无认证API调用 =====
    async getVideoDetailWithoutAuth (videoId) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/Video/Get/${videoId}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async getBatchDetailWithoutAuth (batchId) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/Batch/${batchId}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async getUserVideoProgressWithoutAuth (videoId) {
      // 直接返回模拟数据，不需要认证
      return Promise.resolve({
        success: true,
        data: {
          progress: 0,
          playDuration: 0,
          completed: false
        },
        msg: '观看进度获取成功'
      });
    },

    async createOrGetRecordWithoutAuth (data) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/create-or-get`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: data,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async updateWatchProgressWithoutAuth (userId, data) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/${userId}/watch-progress`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: data,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async startWatchingWithoutAuth (userId, batchId) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/${userId}/${batchId}/start-watching`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async submitAnswerWithoutAuth (userId, data) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/${userId}/submit-answer`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: data,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    async grantRewardWithoutAuth (userId, data) {
      // 直接调用uni.request，不经过认证拦截器
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${getApiBaseURL()}/UserBatchRecord/${userId}/grant-reward`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: data,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    // ===== 用户认证 =====
    async tryWechatAutoLogin (options) {
      try {
        // 使用微信用户服务进行自动登录
        const result = await wechatUserService.tryWechatAutoLogin(options);

        if (result.success) {
          console.log('微信自动登录成功:', result.data.userInfo.nickname);
          return result.data;
        } else {
          throw new Error(result.message || '微信登录失败');
        }
      } catch (error) {
        console.error('微信自动登录失败:', error);
        throw error;
      }
    },

    // ===== 数据加载 =====
    async loadVideoData () {
      try {
        uni.showLoading({
          title: "加载视频中...",
        });

        // 如果有批次ID，先验证批次
        if (this.batchId) {
          await this.validateBatch();
        }

        // 调用API获取视频详情（使用无认证的直接请求）
        const response = await this.getVideoDetailWithoutAuth(this.videoId);

        if (response.success && response.data) {
          const video = response.data;
          console.log('=== 视频数据调试信息 ===');
          console.log('API返回的原始视频数据:', video);
          console.log('video.videoUrl:', video.videoUrl);
          console.log('video.videoUrl 类型:', typeof video.videoUrl);
          console.log('video.videoUrl 是否包含http:', video.videoUrl && (video.videoUrl.includes('http://') || video.videoUrl.includes('https://')));
          console.log('video.coverUrl:', video.coverUrl);

          console.log('=== 开始构建视频URL ===');
          console.log('准备调用 buildCompleteFileUrl，输入参数:', video.videoUrl);
          const videoUrl = this.buildCompleteFileUrl(video.videoUrl);
          console.log('buildCompleteFileUrl 返回结果:', videoUrl);

          console.log('=== 开始构建封面URL ===');
          console.log('准备调用 buildCompleteFileUrl，输入参数:', video.coverUrl);
          const coverUrl = this.buildCompleteFileUrl(video.coverUrl);
          console.log('buildCompleteFileUrl 返回结果:', coverUrl);

          console.log('构建后的视频URL:', videoUrl);
          console.log('构建后的封面URL:', coverUrl);

          this.currentVideo = {
            id: video.id,
            title: video.title,
            cover: coverUrl || '/assets/images/video-cover.jpg',
            url: videoUrl || 'https://www.runoob.com/try/demo_source/mov_bbb.mp4',
            duration: video.duration || 0,
            views: video.viewCount || 0,
            likes: video.likeCount || 0,
            description: video.description || '',
            rewardAmount: video.rewardAmount || 0
          };

          console.log('最终设置的currentVideo:', this.currentVideo);
          console.log('=== 视频数据调试结束 ===');

          this.duration = video.duration || 0;

          // 加载用户观看进度
          await this.loadUserProgress();

          // 加载问题数据（暂时使用模拟数据，后续可以从API获取）
          this.loadQuizData();

          // 如果有分享人ID，记录分享信息
          if (this.sharerId) {
            this.recordSharerInfo();
          }

        } else {
          throw new Error(response.msg || '获取视频详情失败');
        }

        uni.hideLoading();
      } catch (error) {
        console.error('加载视频数据失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: error.message || "加载失败",
          icon: "none",
        });

        // API调用失败，返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    },

    // 加载用户观看进度
    async loadUserProgress () {
      try {
        const response = await this.getUserVideoProgressWithoutAuth(this.videoId);

        if (response.success && response.data) {
          const progress = response.data;
          // API返回的是progress(百分比)和playDuration(播放时长)，需要转换
          this.totalWatchTime = progress.playDuration || 0;
          // 根据进度百分比计算maxWatchTime
          this.maxWatchTime = this.duration > 0 ? Math.round((progress.progress / 100) * this.duration) : 0;

          // 如果有观看进度，可以选择是否从上次位置继续播放
          if (this.maxWatchTime > 0) {
            uni.showModal({
              title: '继续观看',
              content: `检测到您上次观看到 ${this.formatTime(this.maxWatchTime)}，是否从此处继续？`,
              success: (res) => {
                if (res.confirm) {
                  // 用户选择继续，跳转到上次观看位置
                  setTimeout(() => {
                    const videoContext = uni.createVideoContext('myVideo', this);
                    if (videoContext) {
                      videoContext.seek(this.maxWatchTime);
                    }
                  }, 1000);
                }
              }
            });
          }
        }
      } catch (error) {
        console.error('加载用户进度失败:', error);
        // 进度加载失败不影响视频播放，只是从头开始
      }
    },

    // 加载问题数据
    loadQuizData () {
      // 问题数据已在loadVideoData中通过API获取
      // 这个方法保留为空，以防其他地方调用
    },

    // 格式化时间显示
    formatTime (seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // ===== 视频事件处理 =====
    // 视频时间更新
    onTimeUpdate (e) {
      const detail = e.detail || {}
      const currentTime = detail.currentTime || 0
      const duration = detail.duration || this.duration || 0

      // 更新当前播放时间
      this.currentPlayTime = currentTime;

      // 更新时长
      if (duration > 0) {
        this.duration = duration
      }

      // 防止快进：如果当前时间超过最大观看时间+1秒，则回退到最大观看时间
      if (currentTime > this.maxWatchTime + 1) {
        const videoContext = uni.createVideoContext('myVideo', this)
        if (videoContext) {
          videoContext.seek(this.maxWatchTime);
        }
        uni.showToast({
          title: "请完整观看视频，不允许快进",
          icon: "none",
          duration: 2000
        });
        return; // 阻止后续逻辑执行
      }

      // 更新最大观看时间（只有在没有快进的情况下才更新）
      if (currentTime > this.maxWatchTime) {
        this.maxWatchTime = currentTime;
      }

      // 发送时间更新事件给父组件的其他逻辑
      const data = {
        current: currentTime,
        duration: duration
      }

      // 调用原有的进度报告逻辑
      if (this.reportProgress) {
        this.reportProgress(data)
      }
    },

    // 视频播放完成
    onVideoEnded () {
      this.videoCompleted = true;
      this.maxWatchTime = Number.MAX_VALUE;

      // 提示用户可以答题
      uni.showToast({
        title: "视频播放完成，可以答题了",
        icon: "none",
        duration: 2000,
      });
    },

    // 监听用户开始拖拽进度条
    onSeeking (e) {
      const seekTime = e.detail.currentTime || 0;

      // 如果用户试图拖拽到超过最大观看时间的位置，阻止并回退
      if (seekTime > this.maxWatchTime + 1) {
        setTimeout(() => {
          const videoContext = uni.createVideoContext('myVideo', this);
          if (videoContext) {
            videoContext.seek(this.maxWatchTime);
          }
        }, 100);

        uni.showToast({
          title: "不允许快进，请完整观看",
          icon: "none",
          duration: 2000
        });
      }
    },

    // 监听用户完成拖拽进度条
    onSeeked (e) {
      const seekTime = e.detail.currentTime || 0;

      // 再次检查，确保用户没有快进
      if (seekTime > this.maxWatchTime + 1) {
        const videoContext = uni.createVideoContext('myVideo', this);
        if (videoContext) {
          videoContext.seek(this.maxWatchTime);
        }

        uni.showToast({
          title: "已回退到正确位置",
          icon: "none",
          duration: 1500
        });
      }
    },

    // 播放事件
    onPlay () {
      // 视频开始播放
    },

    // 暂停事件
    onPause () {
      // 视频暂停
    },

    // 视频元数据加载完成
    onLoadedMetadata () {
      // 视频元数据加载完成
    },

    // 视频等待数据
    onWaiting () {
      // 视频等待数据
    },

    // 视频可以播放
    onCanPlay () {
      // 视频可以播放
    },

    // 全屏状态变化
    onFullscreenChange (e) {
      this.isFullscreen = e.detail.fullScreen;

      // 全屏时强制横屏
      if (this.isFullscreen) {
        this.lockOrientation()
      } else {
        this.unlockOrientation()
      }
    },

    // 强制横屏
    async lockOrientation () {
      try {
        // 方法1: 标准 Screen Orientation API
        if (screen.orientation && screen.orientation.lock) {
          await screen.orientation.lock('landscape')
          return
        }

        // 方法2: 旧版 screen.lockOrientation
        if (screen.lockOrientation) {
          screen.lockOrientation('landscape')
          return
        }

        // 方法3: webkit 前缀
        if (screen.webkitLockOrientation) {
          screen.webkitLockOrientation('landscape')
          return
        }

        // 方法4: moz 前缀
        if (screen.mozLockOrientation) {
          screen.mozLockOrientation('landscape')
          return
        }
      } catch (error) {
        // 横屏锁定失败，静默处理
      }
    },

    // 解锁屏幕方向
    unlockOrientation () {
      try {
        // 方法1: 标准 Screen Orientation API
        if (screen.orientation && screen.orientation.unlock) {
          screen.orientation.unlock()
          return
        }

        // 方法2: 旧版 screen.unlockOrientation
        if (screen.unlockOrientation) {
          screen.unlockOrientation()
          return
        }

        // 方法3: webkit 前缀
        if (screen.webkitUnlockOrientation) {
          screen.webkitUnlockOrientation()
          return
        }

        // 方法4: moz 前缀
        if (screen.mozUnlockOrientation) {
          screen.mozUnlockOrientation()
          return
        }
      } catch (error) {
        // 屏幕方向解锁失败，静默处理
      }
    },

    // ===== 问答相关 =====
    // 提交答案
    async onQuizSubmit (answerData) {
      try {
        if (!this.batchId) {
          console.warn('缺少批次ID，无法提交答案');
          return;
        }

        console.log('提交答案:', answerData);

        // 调用API提交答案
        const response = await this.submitAnswerWithoutAuth(this.getCurrentUserId(), {
          batchId: this.batchId,
          answers: answerData.answers,
          score: answerData.score,
          isCorrect: answerData.isCorrect,
          completedAt: new Date().toISOString()
        });

        if (response.success) {
          console.log('答案提交成功:', response.data);
          uni.showToast({
            title: '答案提交成功',
            icon: 'success'
          });
        } else {
          console.error('答案提交失败:', response.msg);
          uni.showToast({
            title: '答案提交失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('提交答案失败:', error);
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
      }
    },

    // 答题完成
    async onQuizComplete (rewardData) {
      try {
        console.log('答题完成，奖励信息:', rewardData);

        // 如果有奖励，调用API发放奖励
        if (rewardData && rewardData.rewardAmount > 0) {
          const response = await this.grantRewardWithoutAuth(this.getCurrentUserId(), {
            batchId: this.batchId,
            rewardAmount: rewardData.rewardAmount,
            rewardType: 'quiz_completion',
            description: '完成视频答题奖励'
          });

          if (response.success) {
            console.log('奖励发放成功:', response.data);
            uni.showToast({
              title: `恭喜获得 ${rewardData.rewardAmount} 元奖励！`,
              icon: 'success',
              duration: 3000
            });
          } else {
            console.error('奖励发放失败:', response.msg);
          }
        }

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
      } catch (error) {
        console.error('处理答题完成失败:', error);
        // 即使奖励发放失败，也要返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    },

    // ===== 进度监控相关 =====
    // 开始进度监控
    startProgressMonitoring () {
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
      }

      this.watchStartTime = Date.now();

      // 创建观看记录
      this.createWatchRecord();

      // 每5秒输出一次观看进度
      this.progressTimer = setInterval(() => {
        this.outputWatchProgress();
      }, 5000);
    },

    // 停止进度监控
    stopProgressMonitoring () {
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = null;
      }
    },

    // 输出观看进度
    outputWatchProgress () {
      // 计算实际观看时长
      const now = Date.now();
      if (this.watchStartTime) {
        this.totalWatchTime += (now - this.watchStartTime) / 1000;
        this.watchStartTime = now;
      }

      // 调用API将进度数据发送到服务器
      this.submitWatchProgress();
    },

    // 创建观看记录
    async createWatchRecord () {
      if (!this.batchId) {
        console.warn('缺少批次ID，无法创建观看记录');
        return;
      }

      try {
        // 构建推广链接信息
        let promotionLink = '';
        if (this.sharerId) {
          promotionLink = `shared_by_${this.sharerId}`;
        }

        // 优先使用新的统一API
        const response = await this.createOrGetRecordWithoutAuth({
          batchId: this.batchId,
          userId: this.getCurrentUserId(),
          promotionLink: promotionLink
        });

        if (response.success && response.data) {
          this.viewRecordId = response.data.id;
          console.log('观看记录创建成功:', this.viewRecordId);

          // 开始观看
          await this.startWatchingWithoutAuth(this.getCurrentUserId(), this.batchId);
          return;
        }
      } catch (error) {
        console.error('新API创建观看记录失败，尝试旧API:', error);
      }

      // 回退到旧API
      try {
        const response = await createViewRecord({
          batchId: this.batchId,
          userId: this.getCurrentUserId(),
          promotionLink: ''
        });

        if (response.success && response.data) {
          this.viewRecordId = response.data;
          console.log('观看记录创建成功（旧API）:', this.viewRecordId);
        }
      } catch (error) {
        console.error('创建观看记录失败:', error);
      }
    },

    // 提交观看进度到服务器
    async submitWatchProgress () {
      if (!this.batchId) {
        console.warn('缺少批次ID，无法更新观看进度');
        return;
      }

      try {
        const watchProgress = this.duration > 0 ? (this.maxWatchTime / this.duration) : 0;

        // 优先使用新的统一API
        const response = await this.updateWatchProgressWithoutAuth(this.getCurrentUserId(), {
          batchId: this.batchId,
          viewDuration: Math.floor(this.totalWatchTime),
          watchProgress: watchProgress,
          isCompleted: this.videoCompleted
        });

        if (response.success) {
          console.log('观看进度更新成功（新API）');
          return;
        }
      } catch (error) {
        console.error('新API更新观看进度失败，尝试旧API:', error);
      }

      // 回退到旧API
      try {
        const watchProgress = this.duration > 0 ? (this.maxWatchTime / this.duration) : 0;

        const response = await updateViewProgress({
          batchId: this.batchId,
          viewDuration: Math.floor(this.totalWatchTime),
          watchProgress: watchProgress,
          isCompleted: this.videoCompleted
        });

        if (!response.success) {
          console.error('更新观看进度失败:', response.msg);
        }
      } catch (error) {
        console.error('提交观看进度失败:', error);
      }
    },

    // 获取当前用户ID
    getCurrentUserId () {
      // 直接返回模拟微信用户ID
      return 'mock_wechat_user_001';
    },

    // 初始化模拟微信用户
    initMockWechatUser () {
      // 使用微信用户服务初始化模拟用户
      const result = wechatUserService.initMockWechatUser();
      console.log('模拟微信用户初始化完成:', result.userInfo.nickname);
    },

    // 解析URL参数
    parseUrlParams () {
      try {
        const url = window.location.href;
        console.log('解析URL参数:', url);

        const hash = url.split('#')[1];
        if (hash && hash.includes('?')) {
          const queryString = hash.split('?')[1];
          const params = new URLSearchParams(queryString);

          if (params.get('videoId')) {
            this.videoId = parseInt(params.get('videoId'));
            console.log('设置videoId:', this.videoId);
          }

          if (params.get('batchId')) {
            this.batchId = parseInt(params.get('batchId'));
            console.log('设置batchId:', this.batchId);
          }

          if (params.get('sharerId')) {
            this.sharerId = params.get('sharerId');
            console.log('设置sharerId:', this.sharerId);
          }

          console.log('参数解析完成:', {
            videoId: this.videoId,
            batchId: this.batchId,
            sharerId: this.sharerId
          });
        }
      } catch (error) {
        console.error('解析URL参数失败:', error);
      }
    },

    // 验证批次
    async validateBatch () {
      try {
        console.log('验证批次:', this.batchId);
        const response = await this.getBatchDetailWithoutAuth(this.batchId);

        if (!response.success || !response.data) {
          throw new Error('批次不存在');
        }

        this.batchInfo = response.data;
        const batch = response.data;

        // 检查批次状态
        if (batch.status !== 1) {
          throw new Error('批次未启用');
        }

        // 检查时间范围
        const now = new Date();
        const startTime = new Date(batch.startTime);
        const endTime = new Date(batch.endTime);

        if (now < startTime) {
          throw new Error('批次尚未开始');
        }

        if (now > endTime) {
          throw new Error('批次已结束');
        }

        console.log('批次验证通过:', batch);
      } catch (error) {
        console.error('批次验证失败:', error);
        throw error;
      }
    },

    // 记录分享人信息
    recordSharerInfo () {
      try {
        console.log('记录分享人信息:', this.sharerId);
        // 这里可以调用API记录分享信息，或者在创建观看记录时包含分享人信息
        // 暂时先记录到本地存储
        uni.setStorageSync('currentSharerInfo', {
          sharerId: this.sharerId,
          batchId: this.batchId,
          videoId: this.videoId,
          shareTime: new Date().toISOString()
        });
      } catch (error) {
        console.error('记录分享人信息失败:', error);
      }
    }
  }
}
</script>

<style>
/* 主容器 */
.container {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  min-height: calc(100vh - var(--window-bottom));
  width: 100%;
}

/* 视频播放器样式 */
.video-player {
  width: 100%;
  height: 422rpx;
  background: #000;
  border: 2rpx solid #39BFFD;
  border-radius: 8rpx;
}

/* 内容区域 */
.video-content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

/* 视频信息 */
.video-info {
  background: white;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.video-info .video-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
</style>
